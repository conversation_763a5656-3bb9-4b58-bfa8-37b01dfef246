<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;

class PaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name' => 'Bank Transfer',
                'slug' => 'bank-transfer',
                'description' => 'Direct bank transfer payment method',
                'required_fields' => [
                    'bank_name' => 'Bank Name',
                    'account_number' => 'Account Number',
                    'account_holder_name' => 'Account Holder Name',
                    'routing_number' => 'Routing Number'
                ],
                'icon' => 'fas fa-university',
                'is_active' => 1,
                'sort_order' => 1
            ],
            [
                'name' => 'Credit Card',
                'slug' => 'credit-card',
                'description' => 'Credit card payment processing',
                'required_fields' => [
                    'merchant_id' => 'Merchant ID',
                    'api_key' => 'API Key',
                    'secret_key' => 'Secret Key'
                ],
                'icon' => 'fas fa-credit-card',
                'is_active' => 1,
                'sort_order' => 2
            ],
            [
                'name' => 'Digital Wallet',
                'slug' => 'digital-wallet',
                'description' => 'Digital wallet payment method',
                'required_fields' => [
                    'wallet_id' => 'Wallet ID',
                    'api_token' => 'API Token'
                ],
                'icon' => 'fas fa-wallet',
                'is_active' => 1,
                'sort_order' => 3
            ],
            [
                'name' => 'Cash on Delivery',
                'slug' => 'cash-on-delivery',
                'description' => 'Cash payment upon delivery',
                'required_fields' => [],
                'icon' => 'fas fa-money-bill-wave',
                'is_active' => 1,
                'sort_order' => 4
            ]
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['slug' => $method['slug']],
                $method
            );
        }
    }
}

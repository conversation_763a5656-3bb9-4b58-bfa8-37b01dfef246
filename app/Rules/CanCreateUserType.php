<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CanCreateUserType implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $currentUser = auth()->user();

        if (!$currentUser || !$currentUser->canCreateUserType($value)) {
            $allowedTypes = $currentUser ? $currentUser->getAllowedUserTypesForCreation() : [];
            $fail('You are not authorized to create users of type: ' . $value . '. You can only create: ' . implode(', ', $allowedTypes));
        }
    }
}

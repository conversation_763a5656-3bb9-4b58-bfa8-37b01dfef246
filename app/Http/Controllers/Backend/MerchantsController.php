<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Shop;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MerchantsController extends Controller
{
    # construct
    public function __construct()
    {
        $this->middleware(['permission:merchants'])->only('index');
        $this->middleware(['permission:add_merchants'])->only(['create', 'store']);
        $this->middleware(['permission:edit_merchants'])->only(['edit', 'update']);
        $this->middleware(['permission:delete_merchants'])->only(['delete']);
    }

    # merchant list
    public function index(Request $request)
    {
        $searchKey = null;
        $merchants = User::where('user_type', 'merchant')->with('shop')->latest();

        if ($request->search != null) {
            $merchants = $merchants->where('name', 'like', '%' . $request->search . '%')
                                 ->orWhere('email', 'like', '%' . $request->search . '%');
            $searchKey = $request->search;
        }

        $merchants = $merchants->paginate(paginationNumber());
        return view('backend.pages.merchants.index', compact('merchants', 'searchKey'));
    }

    # return create form
    public function create()
    {
        // Check if current user can create merchants
        if (!auth()->user()->canCreateUserType('merchant')) {
            flash(localize('You are not authorized to create merchants'))->error();
            return redirect()->route('admin.merchants.index');
        }

        return view('backend.pages.merchants.create');
    }

    # save new merchant
    public function store(Request $request)
    {
        // Check if current user can create merchants
        if (!auth()->user()->canCreateUserType('merchant')) {
            flash(localize('You are not authorized to create merchants'))->error();
            return back();
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string',
            'password' => 'required|string|min:6',
            'shop_name' => 'required|string|max:255',
            'shop_address' => 'nullable|string',
            'min_order_amount' => 'nullable|numeric|min:0',
            'admin_commission_percentage' => 'nullable|numeric|min:0|max:100',
            'self_delivery' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // Create user
            $user = new User;
            $user->name = $request->name;
            $user->email = $request->email;
            $user->phone = validatePhone($request->phone);
            $user->user_type = 'merchant';
            $user->password = Hash::make($request->password);
            $user->created_by = auth()->user()->id;
            $user->save();

            // Create shop
            $shop = new Shop;
            $shop->user_id = $user->id;
            $shop->shop_name = $request->shop_name;
            $shop->slug = Str::slug($request->shop_name) . '-' . $user->id;
            $shop->shop_address = $request->shop_address;
            $shop->min_order_amount = $request->min_order_amount ?? 0;
            $shop->admin_commission_percentage = $request->admin_commission_percentage ?? 0;
            $shop->self_delivery = $request->self_delivery ? 1 : 0;
            $shop->is_approved = 1; // Auto-approve admin-created merchants
            $shop->is_verified_by_admin = 1;
            $shop->is_published = 1;
            $shop->save();

            // Update user with shop_id
            $user->shop_id = $shop->id;
            $user->save();

            DB::commit();
            flash(localize('Merchant has been created successfully'))->success();
            return redirect()->route('admin.merchants.index');

        } catch (\Exception $e) {
            DB::rollback();
            flash(localize('Error creating merchant: ' . $e->getMessage()))->error();
            return back()->withInput();
        }
    }
}

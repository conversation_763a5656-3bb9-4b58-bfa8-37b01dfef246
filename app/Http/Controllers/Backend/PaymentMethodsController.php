<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PaymentMethod;
use Illuminate\Support\Str;

class PaymentMethodsController extends Controller
{
    # construct
    public function __construct()
    {
        $this->middleware(['permission:payment_methods'])->only('index');
        $this->middleware(['permission:add_payment_methods'])->only(['create', 'store']);
        $this->middleware(['permission:edit_payment_methods'])->only(['edit', 'update']);
        $this->middleware(['permission:delete_payment_methods'])->only(['delete']);
    }

    # payment methods list
    public function index(Request $request)
    {
        // Only admin can access payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to access payment methods'))->error();
            return redirect()->back();
        }

        $searchKey = null;
        $paymentMethods = PaymentMethod::orderBy('sort_order')->orderBy('name');

        if ($request->search != null) {
            $paymentMethods = $paymentMethods->where('name', 'like', '%' . $request->search . '%');
            $searchKey = $request->search;
        }

        $paymentMethods = $paymentMethods->paginate(paginationNumber());
        return view('backend.pages.payment-methods.index', compact('paymentMethods', 'searchKey'));
    }

    # return create form
    public function create()
    {
        // Only admin can create payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to create payment methods'))->error();
            return redirect()->route('admin.payment-methods.index');
        }

        return view('backend.pages.payment-methods.create');
    }

    # save new payment method
    public function store(Request $request)
    {
        // Only admin can create payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to create payment methods'))->error();
            return back();
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'required_fields' => 'nullable|array',
            'icon' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $paymentMethod = new PaymentMethod;
        $paymentMethod->name = $request->name;
        $paymentMethod->slug = Str::slug($request->name);
        $paymentMethod->description = $request->description;
        $paymentMethod->required_fields = $request->required_fields;
        $paymentMethod->icon = $request->icon;
        $paymentMethod->is_active = $request->is_active ? 1 : 0;
        $paymentMethod->sort_order = $request->sort_order ?? 0;
        $paymentMethod->save();

        flash(localize('Payment method has been created successfully'))->success();
        return redirect()->route('admin.payment-methods.index');
    }

    # edit payment method
    public function edit($id)
    {
        // Only admin can edit payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to edit payment methods'))->error();
            return redirect()->route('admin.payment-methods.index');
        }

        $paymentMethod = PaymentMethod::findOrFail($id);
        return view('backend.pages.payment-methods.edit', compact('paymentMethod'));
    }

    # update payment method
    public function update(Request $request, $id)
    {
        // Only admin can update payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to update payment methods'))->error();
            return back();
        }

        $paymentMethod = PaymentMethod::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'required_fields' => 'nullable|array',
            'icon' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $paymentMethod->name = $request->name;
        $paymentMethod->slug = Str::slug($request->name);
        $paymentMethod->description = $request->description;
        $paymentMethod->required_fields = $request->required_fields;
        $paymentMethod->icon = $request->icon;
        $paymentMethod->is_active = $request->is_active ? 1 : 0;
        $paymentMethod->sort_order = $request->sort_order ?? 0;
        $paymentMethod->save();

        flash(localize('Payment method has been updated successfully'))->success();
        return back();
    }

    # delete payment method
    public function delete($id)
    {
        // Only admin can delete payment methods
        if (auth()->user()->user_type !== 'admin') {
            flash(localize('You are not authorized to delete payment methods'))->error();
            return back();
        }

        PaymentMethod::destroy($id);
        flash(localize('Payment method has been deleted successfully'))->success();
        return redirect()->route('admin.payment-methods.index');
    }
}

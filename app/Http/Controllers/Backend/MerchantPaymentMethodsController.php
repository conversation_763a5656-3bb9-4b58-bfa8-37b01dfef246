<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PaymentMethod;
use App\Models\MerchantPaymentMethod;

class MerchantPaymentMethodsController extends Controller
{
    # construct
    public function __construct()
    {
        $this->middleware(['permission:merchant_payment_methods'])->only('index');
        $this->middleware(['permission:add_merchant_payment_methods'])->only(['create', 'store']);
        $this->middleware(['permission:edit_merchant_payment_methods'])->only(['edit', 'update']);
        $this->middleware(['permission:delete_merchant_payment_methods'])->only(['delete']);
    }

    # merchant payment methods list
    public function index(Request $request)
    {
        // Only merchants can access their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to access merchant payment methods'))->error();
            return redirect()->back();
        }

        $merchantId = auth()->user()->id;
        $searchKey = null;
        $merchantPaymentMethods = MerchantPaymentMethod::where('merchant_id', $merchantId)
                                                      ->with('paymentMethod')
                                                      ->latest();

        if ($request->search != null) {
            $merchantPaymentMethods = $merchantPaymentMethods->whereHas('paymentMethod', function($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            });
            $searchKey = $request->search;
        }

        $merchantPaymentMethods = $merchantPaymentMethods->paginate(paginationNumber());
        return view('backend.pages.merchant-payment-methods.index', compact('merchantPaymentMethods', 'searchKey'));
    }

    # return create form
    public function create()
    {
        // Only merchants can create their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to create merchant payment methods'))->error();
            return redirect()->route('admin.merchant-payment-methods.index');
        }

        $merchantId = auth()->user()->id;

        // Get available payment methods that merchant hasn't configured yet
        $availablePaymentMethods = PaymentMethod::active()
                                               ->whereNotIn('id', function($query) use ($merchantId) {
                                                   $query->select('payment_method_id')
                                                         ->from('merchant_payment_methods')
                                                         ->where('merchant_id', $merchantId);
                                               })
                                               ->get();

        return view('backend.pages.merchant-payment-methods.create', compact('availablePaymentMethods'));
    }

    # save new merchant payment method
    public function store(Request $request)
    {
        // Only merchants can create their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to create merchant payment methods'))->error();
            return back();
        }

        $request->validate([
            'payment_method_id' => 'required|exists:payment_methods,id',
            'configuration' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $merchantId = auth()->user()->id;

        // Check if merchant already has this payment method
        $existing = MerchantPaymentMethod::where('merchant_id', $merchantId)
                                        ->where('payment_method_id', $request->payment_method_id)
                                        ->first();

        if ($existing) {
            flash(localize('You have already configured this payment method'))->error();
            return back();
        }

        $merchantPaymentMethod = new MerchantPaymentMethod;
        $merchantPaymentMethod->merchant_id = $merchantId;
        $merchantPaymentMethod->payment_method_id = $request->payment_method_id;
        $merchantPaymentMethod->configuration = $request->configuration;
        $merchantPaymentMethod->is_active = $request->is_active ? 1 : 0;
        $merchantPaymentMethod->save();

        flash(localize('Payment method has been configured successfully'))->success();
        return redirect()->route('admin.merchant-payment-methods.index');
    }

    # edit merchant payment method
    public function edit($id)
    {
        // Only merchants can edit their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to edit merchant payment methods'))->error();
            return redirect()->route('admin.merchant-payment-methods.index');
        }

        $merchantPaymentMethod = MerchantPaymentMethod::where('merchant_id', auth()->user()->id)
                                                     ->with('paymentMethod')
                                                     ->findOrFail($id);

        return view('backend.pages.merchant-payment-methods.edit', compact('merchantPaymentMethod'));
    }

    # update merchant payment method
    public function update(Request $request, $id)
    {
        // Only merchants can update their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to update merchant payment methods'))->error();
            return back();
        }

        $merchantPaymentMethod = MerchantPaymentMethod::where('merchant_id', auth()->user()->id)
                                                     ->findOrFail($id);

        $request->validate([
            'configuration' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $merchantPaymentMethod->configuration = $request->configuration;
        $merchantPaymentMethod->is_active = $request->is_active ? 1 : 0;
        $merchantPaymentMethod->save();

        flash(localize('Payment method configuration has been updated successfully'))->success();
        return back();
    }

    # delete merchant payment method
    public function delete($id)
    {
        // Only merchants can delete their payment methods
        if (auth()->user()->user_type !== 'merchant') {
            flash(localize('You are not authorized to delete merchant payment methods'))->error();
            return back();
        }

        MerchantPaymentMethod::where('merchant_id', auth()->user()->id)->where('id', $id)->delete();
        flash(localize('Payment method configuration has been deleted successfully'))->success();
        return redirect()->route('admin.merchant-payment-methods.index');
    }
}

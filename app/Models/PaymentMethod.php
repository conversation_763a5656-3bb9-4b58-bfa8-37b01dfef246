<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'required_fields',
        'icon',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'required_fields' => 'array',
        'is_active' => 'boolean'
    ];

    # scope for active payment methods
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    # merchants using this payment method
    public function merchants()
    {
        return $this->belongsToMany(User::class, 'merchant_payment_methods', 'payment_method_id', 'merchant_id')
                    ->withPivot('configuration', 'is_active')
                    ->withTimestamps();
    }

    # merchant payment methods
    public function merchantPaymentMethods()
    {
        return $this->hasMany(MerchantPaymentMethod::class);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantPaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'merchant_id',
        'payment_method_id',
        'configuration',
        'is_active'
    ];

    protected $casts = [
        'configuration' => 'array',
        'is_active' => 'boolean'
    ];

    # merchant relationship
    public function merchant()
    {
        return $this->belongsTo(User::class, 'merchant_id');
    }

    # payment method relationship
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    # scope for active merchant payment methods
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }
}
